const fs = require('fs');
const https = require('https');
const http = require('http');
const path = require('path');
const url = require('url');

// 讀取 script.txt 文件
const scriptContent = fs.readFileSync('script.txt', 'utf8');

// 使用正則表達式提取所有 previewImage URL
const previewImageRegex = /previewImage:\s*['"]([^'"]+)['"]/g;
const urls = [];
let match;

while ((match = previewImageRegex.exec(scriptContent)) !== null) {
    urls.push(match[1]);
}

console.log(`找到 ${urls.length} 個圖片 URL`);

// 下載圖片的函數
function downloadImage(imageUrl, filename) {
    return new Promise((resolve, reject) => {
        const parsedUrl = url.parse(imageUrl);
        const protocol = parsedUrl.protocol === 'https:' ? https : http;

        const file = fs.createWriteStream(filename);

        protocol.get(imageUrl, (response) => {
            // 處理重定向
            if (response.statusCode === 301 || response.statusCode === 302) {
                file.close();
                fs.unlink(filename, () => {});
                return downloadImage(response.headers.location, filename)
                    .then(resolve)
                    .catch(reject);
            }

            if (response.statusCode === 200) {
                response.pipe(file);
                file.on('finish', () => {
                    file.close();
                    console.log(`已下載: ${filename}`);
                    resolve();
                });
            } else {
                file.close();
                fs.unlink(filename, () => {});
                reject(new Error(`HTTP ${response.statusCode}: ${imageUrl}`));
            }
        }).on('error', (err) => {
            file.close();
            fs.unlink(filename, () => {}); // 刪除不完整的文件
            reject(err);
        });
    });
}

// 下載所有圖片
async function downloadAllImages() {
    for (let i = 0; i < urls.length; i++) {
        const imageUrl = urls[i];
        const urlParts = url.parse(imageUrl);
        const extension = path.extname(urlParts.pathname) || '.jpg';
        const filename = `${i + 1}${extension}`;
        
        try {
            await downloadImage(imageUrl, filename);
        } catch (error) {
            console.error(`下載失敗 ${filename}: ${error.message}`);
        }
    }
    
    console.log('所有圖片下載完成！');
}

downloadAllImages();
