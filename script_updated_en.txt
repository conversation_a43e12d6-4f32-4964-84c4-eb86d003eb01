    const quickPrompts = [
        {
            title: 'Separate 3D Model',
            imageId: '1.jpg',
            previewImage:
                'https://github.com/PicoTrex/Awesome-Nano-Banana-images/blob/main/images/case4/output.jpg?raw=true',
            prompt: 'Create the image in daylight and isometric view only [architecture]',
            author: '@Zieeett',
            mode: 'edit'
        },
        {
            title: 'One-Click Smart Photo Enhancement',
            imageId: '2.jpg',
            previewImage:
                'https://github.com/PicoTrex/Awesome-Nano-Banana-images/blob/main/images/case7/output.jpg?raw=true',
            prompt: 'This photo is boring and bland. Enhance it! Increase contrast, boost colors, improve lighting to make it richer, you can crop and remove details that affect the composition',
            author: '@op7418',
            mode: 'edit'
        },
        {
            title: 'Switch to Overhead View',
            imageId: '3.jpg',
            previewImage:
                'https://github.com/PicoTrex/Awesome-Nano-Banana-images/blob/main/images/case9/output.jpg?raw=true',
            prompt: 'Convert the photo to an overhead view and mark the photographer\'s position',
            author: '@op7418',
            mode: 'edit'
        },
        {
            title: 'Anime Expressions',
            imageId: '4.png',
            previewImage: 'https://i.mji.rip/2025/09/04/efc060e59e2d9c2e4a137db8564fc492.png',
            prompt: 'Character emotions sheet, multiple expressions of the provided character, featuring happy, sad, angry, surprised, shy, confused, playful, disgusted, thoughtful, crying, and embarrassed. Full set of emotions, clear and distinct expressions, clean background',
            author: '@Gorden_Sun',
            mode: 'edit'
        },
        {
            title: 'Batch Hairstyle Change',
            imageId: '5.jpg',
            previewImage:
                'https://github.com/PicoTrex/Awesome-Nano-Banana-images/blob/main/images/case15/output.jpg?raw=true',
            prompt: 'Generate portraits of this person with different hairstyles in a nine-grid format',
            author: '@balconychy',
            mode: 'edit'
        },
        {
            title: 'Outfit Change',
            imageId: '6.png',
            previewImage: 'https://i.mji.rip/2025/09/04/b9c7402974fba6627ab1b0bf3fce065d.png',
            prompt: `Replace the clothing of the person in the input image with the target clothing shown in the reference image. Keep the person's pose, facial expression, background, and overall realism unchanged. Make the new clothing look natural, well-fitted, and consistent with lighting and shadows. Don't change the person's identity or environment—only change the clothes`,
            author: '@skirano',
            mode: 'edit'
        },
        {
            title: 'Hairstyle Change',
            imageId: '7.png',
            previewImage: 'https://i.mji.rip/2025/09/04/c4dffca8a2916cd1fbefa21237751b81.png',
            prompt: `Please carefully analyze the photo I provided. Your task is to change the hairstyle of the main person in the photo to a new one, while strictly following these rules:
1. **Identity Preservation**: Must completely preserve the person's facial features, facial structure, skin texture, and expression to ensure it looks like the same person.
2. **Background Unchanged**: The background, environment, and lighting conditions where the person is located must remain unchanged.
3. **Body Posture Unchanged**: The person's head posture, body pose, and clothing must remain unchanged.
4. **Seamless Integration**: The new hairstyle needs to be intelligently adjusted according to the person's head shape, face shape, and on-site lighting to ensure that the hair texture, luster, and shadows perfectly blend with the original photo, achieving a highly realistic and seamless effect.

---
**Women's Hairstyle References:**
* Flowing long straight hair
* Romantic wavy curls
* Playful short bob
* Elegant French bangs with shoulder-length hair
* Exquisite vintage updo
* Chic and neat pixie cut
* Fluffy afro curls
* High ponytail
* Dreadlocks
* Silver-grey ombre long hair

**Men's Hairstyle References:**
* Classic business slick-back
* Modern textured short hair / Quiff
* Clean buzz cut
* Retro middle part hairstyle
* Fluffy Korean-style curly hair
* Casual shoulder-length long hair
* Undercut (sides shaved short, top left long)
* Mohawk
* Man bun
---

Please change the person's hairstyle to: Playful short bob`,
            author: 'Official',
            mode: 'edit'
        },
        {
            title: 'Time Filter',
            imageId: '8.png',
            previewImage: 'https://i.mji.rip/2025/09/04/281360a8257436f6ad0b5e56b0982deb.png',
            prompt: `Please reimagine the person in the photo to completely match the style of a specific era. This includes the person's clothing, hairstyle, the overall image quality and filters and composition of the photo, as well as the overall aesthetic style unique to that era. The final output must be a highly realistic image that clearly shows the person.

Target era: 1900`,
            author: 'Official',
            mode: 'edit'
        },
        {
            title: 'Black and White Artistic Portrait',
            imageId: '9.png',
            previewImage: 'https://i.mji.rip/2025/09/04/f03851e1fbea897dee75a109d497e2c7.png',
            prompt: `High-resolution black and white portrait photography work, using editorial and artistic photography style. Keep the person's facial features consistent, only changing posture and composition. Background is a soft gradient from medium gray to near pure white, with delicate film grain texture, creating the atmosphere of classic black and white imagery.
The subject wears a black T-shirt, appearing in different random poses: hand touching face, fingers interlaced across chest, partially covering face with hand, lightly touching chin, etc., emphasizing natural, elegant hand movements. The face still retains the original expression, only showing changes in angle and lighting, capturing details of eyes, cheekbones, or lip corners.
Lighting is gentle directional light, softly outlining the texture of face, hands, and T-shirt; the composition is simple with large areas of negative space. No text or logos, only light, shadow, posture, and emotion intertwined.
The overall atmosphere is intimate and eternal, like a pause between breathing or contemplation, captured as a poetic moment.`,
            author: 'LinuxDO@Bensong',
            mode: 'edit'
        },
        {
            title: '3D Model Figure (Complex Background)',
            imageId: '10.png',
            previewImage: 'https://i.mji.rip/2025/09/04/a5fb782fded5b3778e2b39a455aa1fad.png',
            prompt: `Generate a high-quality, photorealistic 3D model figure image from the user-provided 2D image. The figure should be expertly crafted, meticulously capturing the essence and design of the 2D character illustration.

**Figure Details:**

- **Pose:** The figure's pose should be dynamic, reflecting the original character's action or iconic posture from the 2D image.
- **Clothing & Accessories:** Faithfully reproduce all clothing, armor, accessories (such as weapons, jewelry, headwear) and complex patterns of the 2D character in three-dimensional form, with realistic textures (such as fabric wrinkles, metallic luster, leather texture).
- **Hair:** Hair should be sculpted with flowing and realistic styling that matches the character's hairstyle and length.
- **Facial Features:** The figure's face should accurately present the character's expression and facial features.
- **Material Appearance:** The figure should present the texture of a well-made plastic or resin model, with subtle reflections and appropriate material luster.

**Scene & Display:**

- **Environment:** The figure is placed on a clean and organized desktop, using a circular transparent acrylic base, like a model enthusiast's studio.
- **Background Screen:** In the background behind the figure, there is a computer monitor displaying the 3D model Blender modeling process of this character, showing its digital model. The screen should emit a soft glow, illuminating part of the desktop.
- **Packaging Box:** Next to the figure, slightly tilted toward the viewer, is the figure's retail packaging box. The packaging box's art design should feature the same character in a similar pose to the figure, with stylized brand names (such as "Good Smile Company" or similar fictional brands), possibly with character names. The packaging box should look professionally designed.
- **Desktop Items:** Around the figure and packaging box on the desktop, various model tools and supplies are scattered, which can include:
  - Small paint cans or paint bottles
  - Fine brushes
  - Model tools (such as tweezers, small cutting knives, carving tools)
  - Cutting mat with grid lines
  - Reference books or albums
  - Other small art-related items that suggest a creative workspace.
- **Lighting:** The scene should be lit with soft natural light or studio-like lighting, highlighting the figure's details and creating soft shadows for depth and realism.

**Overall Aesthetics:** The image should convey the feeling of a completed, professionally made collectible figure, displayed in the context of its creation and development. The focus is on the figure itself, but the surrounding elements enhance the narrative of its creation and display.
`,
            author: 'LinuxDO@DT2025',
            mode: 'edit'
        },
        {
            title: 'Retro Propaganda Poster',
            imageId: '11.jpg',
            previewImage:
                'https://camo.githubusercontent.com/d8ee52518aa3db45867fbaac63b4b57f6ad2e24e96a7519bab0c306747c0da21/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d74685a656a4d675830504752316e50796a315a33742e706e673f763d31',
            prompt: "Retro propaganda poster style, highlighting Chinese text, background with red and yellow radial pattern. A beautiful young woman in the center of the image, drawn in exquisite retro style, with a smile, elegant temperament, and affinity. The theme is an advertisement for GPT's latest AI drawing service, emphasizing 'Amazing price 9.9/piece', 'Suitable for various scenarios, image fusion, local redrawing', 'Submit 3 modifications per piece', 'AI direct output, no modification needed', prominently marked at the bottom 'If interested, click \"I want it\" in the lower right', with a finger clicking button action drawn in the lower right corner, and the OpenAI logo displayed in the lower left corner.",
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Custom Anime Figure',
            imageId: '12.jpg',
            previewImage:
                'https://camo.githubusercontent.com/db8e5dc52a7c7d814c573877ee03225c4d4e761d0d987fbec05f1c8f3be8ebe2/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d71564b36666d2d66503861342d4c7870614e374a692e706e673f763d31',
            prompt: 'Generate a photo of an anime-style figure placed on a desktop, presented from a casual, relaxed perspective as if taken casually with a phone. The figure model is based on the person in the attached photo, accurately reproducing the full-body pose, facial expression, and clothing style of the person in the photo, ensuring the figure is presented in full body. The overall design is exquisite and delicate, with hair and clothing using natural and soft gradient colors and delicate textures, leaning towards Japanese anime style, rich in detail, realistic texture, and beautiful appearance.',
            author: '@dotey',
            mode: 'edit'
        },
        {
            title: 'Custom Q-Version Keychain',
            imageId: '13.jpg',
            previewImage:
                'https://camo.githubusercontent.com/0749f414a01d6b6e053e86e0edd1877d1c7a5666683b04071da0115cf7830653/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d424f4877686d6b2d482d4c785133393865706b50702e706e673f763d31',
            prompt: 'A close-up photo showing a cute, colorful keychain being held by a human hand. The keychain is shaped like a Q-version style of [reference image]. The keychain is made of soft rubber material with thick black outlines, attached to a small silver key ring, with a neutral-toned background.',
            author: '@azed_ai',
            mode: 'edit'
        },
        {
            title: 'Golden Pendant Necklace',
            imageId: '14.jpg',
            previewImage:
                'https://camo.githubusercontent.com/ef76535afd5a80239f3a4da844f5ffd07882b93ad2b27e8d12850061cb330a22/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d6a79516c7674765963795957753631585f63576b362e706e673f763d31',
            prompt: 'A photorealistic close-up image showing a gold pendant necklace held by a woman\'s hand. The pendant is engraved with a relief pattern of [image/emoji], hanging on a polished gold chain. The background is a soft, blurred neutral beige tone, using natural lighting, realistic skin tone, product photography style, 16:9 aspect ratio.',
            author: '@azed_ai',
            mode: 'edit'
        },
        {
            title: 'Original Pokémon Generation',
            imageId: '15.jpg',
            previewImage:
                'https://camo.githubusercontent.com/c4a11a7e1012e9f7b1c0a9da081507a923ebc5d9f2a1f02e1b552a6f398d3060/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d306679596f3764325663337566486e5855306678682e706e673f763d31',
            prompt: 'Create an original creature based on this object (provided photo). The creature should look like it belongs to a fantasy monster-catching universe, with a cute or cool design influenced by retro Japanese RPG monster art. The image must include:\\n  – A full-body view of the creature, inspired by the object\'s shape, material, or purpose.\\n  – A small sphere or capsule at its feet (similar to a Poké Ball), with design patterns and colors that match the object\'s appearance—not a standard Poké Ball, but a custom design.\\n  – An invented name for the creature, displayed next to or below it. – Its elemental type (e.g., fire, water, metal, nature, electric...), based on the object\'s core attributes. The illustration should look like it\'s from a fantasy creature encyclopedia, with clean lines, soft shadows, and expressive, character-driven design.',
            author: '@Anima_Labs',
            mode: 'edit'
        },
        {
            title: '3D Q-Version University Personification',
            imageId: '16.jpg',
            previewImage:
                'https://camo.githubusercontent.com/a4ec79c77aa9d82a3ac05572963439535987464070ab3a0f18f05b6cf28a1484/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4a6e687479666157524c4a34387079314673324c382e706e673f763d31',
            prompt: 'Draw a personified 3D Q-version beautiful girl image for {Northwestern Polytechnical University}, embodying the school\'s {aviation, aerospace, and marine three-navigation} characteristics',
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Logo-Shaped Creative Bookshelf',
            imageId: '17.jpg',
            previewImage:
                'https://camo.githubusercontent.com/8c9656afeca8088a32f1e33e896fcac050ca4a69dd854ced4df32b903727df74/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d6833675f616a7455356873373059793074736b694e2e706e673f763d31',
            prompt: 'Photograph a modern bookshelf whose design is inspired by the shape of [LOGO]. The bookshelf consists of smooth, interconnected curves forming multiple compartments of varying sizes. The overall material is smooth matte black metal with wooden shelves inside the curves. Soft warm LED strips outline the inner curve contours. The bookshelf is mounted on a neutral-toned wall, filled with colorful books, small green plants, and minimalist art pieces. The overall atmosphere is creative, elegant, and slightly futuristic.',
            author: '@umesh_ai',
            mode: 'generate'
        },
        {
            title: 'Silhouette Art',
            imageId: '18.jpg',
            previewImage:
                'https://camo.githubusercontent.com/cb6e5f986b2031c8eb3953f29fa01733c18907ef1a2828e72674d9c28bbe5b2f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d576e46454552544a4a626e4470636a4b64623335552e706e673f763d31',
            prompt: 'A basic outline silhouette of an [Eastern Dragon]. Background is bright yellow, silhouette is solid black fill.',
            author: '@umesh_ai',
            mode: 'generate'
        },
        {
            title: 'Frosted Glass Silhouette Contrast',
            imageId: '19.jpg',
            previewImage:
                'https://camo.githubusercontent.com/39b333bbe057c8bfbbec026f843b2cfb9d7a399ae63eef6121839731786ecb0c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d7044736142346f5f6f694e496a75645f6a357970702e706e673f763d31',
            prompt: 'A black and white photograph showing a blurred silhouette of a [subject] behind a frosted or translucent surface. Their [part] outline is clear, pressed against the surface, contrasting sharply with the rest of their hazy, blurred figure. The background is a soft gray gradient, enhancing the mysterious and artistic atmosphere.',
            author: '@umesh_ai',
            mode: 'generate'
        },
        {
            title: 'Selfie-Generated Bobblehead',
            imageId: '20.jpg',
            previewImage:
                'https://camo.githubusercontent.com/65210cc20d1ddd967e05e0cc20805dccceda04f3d30991e2e1925a8f86b54b1c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d654a7a7761703765374b7353774a6f665933696a382e706e673f763d31',
            prompt: 'Turn this photo into a bobblehead: head slightly enlarged, keeping face accurate, body cartoonized. [Place it on a bookshelf].',
            author: '@thisdudelikesAI',
            mode: 'edit'
        },
        {
            title: 'Three Animals Landmark Selfie',
            imageId: '21.jpg',
            previewImage:
                'https://camo.githubusercontent.com/fb16e65d547095227d221354766db4c0ee775c9d5247e6337fe1397f0ee42d3b/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d794164365071514d7438365658364e68315146436c2e706e673f763d31',
            prompt: 'A close-up selfie of three [animal types] in front of the iconic [landmark], each with different expressions, shot during golden hour with cinematic lighting. The animals are close to the camera, heads together, mimicking selfie poses, showing expressions of joy, surprise, and calm. The background shows the complete architectural details of the [landmark], with soft lighting and warm atmosphere. Shot in photographic, realistic cartoon style, high detail, 1:1 aspect ratio.',
            author: '@berryxia_ai',
            mode: 'generate'
        },
        {
            title: 'Perspective 3D Pop-out Effect',
            imageId: '22.jpg',
            previewImage:
                'https://camo.githubusercontent.com/ff60c97d59ac8dcf08130db0dc8dc94f22222cee56f80800d4950e53170facd6/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d5a775834584a427a3542714d63764f585963656e302e706e673f763d31',
            prompt: 'Ultra-realistic, shot from above looking down, a beautiful Instagram model [Anne Hathaway / see reference image], with exquisite beautiful makeup and fashionable styling, standing on a smartphone screen held by someone, creating a strong perspective illusion. Emphasize the three-dimensional effect of the girl standing out from the phone. She wears black-framed glasses, dressed in street style, playfully posing cutely. The phone screen is processed as a dark floor, like a small stage. The scene uses strong forced perspective to show the proportion difference between palm, phone, and girl. Background is clean gray, using soft indoor lighting, shallow depth of field, overall style is surreal realistic composite. Particularly strong perspective',
            author: '@ZHO_ZHO_ZHO',
            mode: 'edit'
        },
        {
            title: 'Google Maps to Ancient Treasure Map',
            imageId: '23.jpg',
            previewImage:
                'https://camo.githubusercontent.com/2b53ef31557db7f68efd49a9b95e6cf04e40863e7bfc5878ab654ec7056283fa/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d6a5058363570464c424d54767a56614367536a6c7a2e706e673f763d31',
            prompt: 'Transform the image into an ancient treasure map drawn on old parchment. The map contains detailed elements such as sailing ships on the ocean, ancient ports or castles on coastlines, dotted paths leading to a large "X" marking the treasure location, mountains, palm trees, and a decorative compass rose. The overall style is reminiscent of old pirate adventure movies.',
            author: '@umesh_ai',
            mode: 'edit'
        },
        {
            title: 'Branded Keyboard Keycaps',
            imageId: '24.jpg',
            previewImage:
                'https://camo.githubusercontent.com/08cd326bf7298cec0e74668e927df19988dd715cbe5968a82583554bf8a8fd1b/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d712d35377a6149545362376435593231596136526b2e706e673f763d31',
            prompt: 'An ultra-realistic 3D render showing four mechanical keyboard keycaps arranged in a tight 2x2 grid, all keycaps touching each other. Viewed from an isometric angle. One keycap is transparent with "{just}" printed in red. The other three keycaps use colors: {black, purple, and white}. One keycap has the Github logo. The other two keycaps have "{fork}" and "{it}" written on them respectively. Realistic plastic texture, rounded sculpted keycaps, soft shadows, clean light gray background.',
            author: '@egeberkina',
            mode: 'generate'
        },
        {
            title: 'Miniature Tilt-Shift Photography',
            imageId: '25.jpg',
            previewImage:
                'https://camo.githubusercontent.com/e10a8da63bb593ebe441a539072aee82de9a2a03dea0420002133fe0a23980eb/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d64346d7443793244755a6b32626b5869444d6f4f532e706e673f763d31',
            prompt: 'Ultra-high detail miniature [Cyberpunk] landscape viewed from above, using tilt-shift lens effects. The scene is filled with toy-like elements, all rendered in high-resolution CG. Dramatic lighting creates a cinematic atmosphere with vivid colors and strong contrast, emphasizing depth of field effects and realistic micro perspective, making viewers feel like they\'re looking down at a toy world-like miniature reality. The image contains numerous visual gags and details with high replay value',
            author: '@terry623',
            mode: 'generate'
        },
        {
            title: 'Chrome Emoji Badge',
            imageId: '26.jpg',
            previewImage:
                'https://camo.githubusercontent.com/4a973f06b0fc116ae1f42b5a5ec370f08ae0606212cd5f5e1b6e2fd5e1724b06/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d2d51577a495941744f374b433348746868736a48302e706e673f763d31',
            prompt: 'High-precision 3D render showing a metallic badge according to emoji icon {👍}, mounted on a vertical product card, with ultra-smooth chrome texture and rounded 3D icon styling, stylized futuristic design with soft reflections and clean shadows. The paper card has a punched European-style hanging hole at the top center, with a prominent title "{Awesome}" above the badge and a fun slogan "{Smash that ⭐ if you like it!}" below. Background is soft gray, using soft studio lighting, overall minimalist style.',
            author: '@egeberkina',
            mode: 'generate'
        },
        {
            title: 'Children\'s Coloring Page Illustration',
            imageId: '27.jpg',
            previewImage:
                'https://camo.githubusercontent.com/77d141c0b20a88c50ae0f517d829f92e0743f32220e023b795eba354669d6167/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d76566f56516b367256514246326247554d47664f722e706e673f763d31',
            prompt: `A black and white line drawing coloring illustration, suitable for direct printing on standard size (8.5x11 inches) paper, without paper borders. The overall illustration style is fresh and simple, using clear and smooth black outline lines, no shadows, no grayscale, no color fill, pure white background, convenient for coloring.
[Also, to help users who don't know how to color, please generate a complete colored version as a small image in the lower right corner for reference]
Target audience: [6-9 year old children]
Scene description:
[A unicorn walking on the grass in the forest, sunny day, blue sky and white clouds]`,
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Letter and Word Meaning Fusion',
            imageId: '28.jpg',
            previewImage:
                'https://camo.githubusercontent.com/ab4ecc5f919ae1f59f23dd6dbc42cb6e75aaecc7369943cc4346a2958d2efbc7/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d646b335630734c4c54443966515730444d3970786e2e706e673f763d31',
            prompt: `Integrate the meaning of the word into the letters, cleverly combining graphics and letters.
Word: { beautify }
Add a brief explanation of the word below`,
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Double Exposure',
            imageId: '29.jpg',
            previewImage:
                'https://camo.githubusercontent.com/6e87a6abd6bce57d9e0e9fa763e8172920e3a99769b32314adcbe60f1dccb5a4/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d7557417442794a353434636452356e426a4d4d33482e706e673f763d31',
            prompt: 'Double exposure, Midjourney style, fused, blended, overlaid double exposure image, double exposure style. An outstanding masterpiece by Yukisakura, showing a wonderful double exposure composition that harmoniously interweaves the silhouette of Aragorn, son of Arathorn, with the visually striking, rugged landscape of Middle-earth in vibrant spring. The scene of sun-drenched pine forests, mountain peaks, and a lone horse traversing the path echoes outward from the texture of his figure, adding layers of narrative and solitude. As the clean, distinct monochrome background maintains sharp contrast, wonderful tension gradually forms, drawing all focus to the richly layered double exposure. It features a vibrant full-color scheme within Aragorn\'s silhouette, and clear, deliberate lines that trace each contour with emotional precision. (Detailed:1.45). (Detailed background:1.4).',
            author: 'rezzycheck',
            mode: 'generate'
        },
        {
            title: 'Surreal Interactive Scene',
            imageId: '30.jpg',
            previewImage:
                'https://camo.githubusercontent.com/cd6872f458c49d960a9995a18d66d631fc7ca4c4725ffdde68c1ff3b631ee6b4/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d6e76414571617876736c446e2d703268434b62484e2e706e673f763d31',
            prompt: 'A pencil sketch depicting a scene where [Subject 1] interacts with [Subject 2], where [Subject 2] is presented in realistic full-color style, creating a surreal contrast with the hand-drawn sketch style of [Subject 1] and the background.',
            author: '@umesh_ai',
            mode: 'generate'
        },
        {
            title: 'Animal Silicone Wrist Rest',
            imageId: '31.jpg',
            previewImage:
                'https://camo.githubusercontent.com/f047543ae11e9311c97619f4c0f4c6e85e8f755fa54860efc75cbe9806c6c0f6/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d4e567365347a566867596f4b5347764e5f5f792d6d2e706e673f763d31',
            prompt: 'Create an image of a cute Q-version silicone wrist rest, shaped based on the [🐼] emoji, made of soft food-grade silicone material with a skin-friendly matte texture, filled with slow-rebound cotton inside, anthropomorphic cartoon style with vivid expressions, arms spread open lying on the desktop, presenting a wrist-hugging posture, overall shape is round and soft, colored in [🐼] color scheme, healing and cute style, suitable for office use, white pure color background, soft lighting, product photography style, front view or 45-degree overhead view, high-definition details, highlighting silicone texture and comfort function',
            author: '@ZHO_ZHO_ZHO',
            mode: 'generate'
        },
        {
            title: 'Glowing Line Anatomy Diagram',
            imageId: '32.jpg',
            previewImage:
                'https://camo.githubusercontent.com/8270aa4a29f6ff2256fd4130a1fceb7b54e6df26269c91001ece8e6d705e6450/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d756b6e5f617a747a413479544e474e46315143546c2e706e673f763d31',
            prompt: 'A digital illustration depicting a [SUBJECT], whose structure is outlined by a set of glowing, clean, and pure blue lines. The image is set against a dark background to highlight the [SUBJECT]\'s form and characteristics. A specific part, such as [PART], is emphasized with a red glow to indicate the importance or special significance of that area. The overall style is both educational and visually appealing, designed as if it were an advanced imaging technology.',
            author: '@umesh_ai',
            mode: 'generate'
        },
        {
            title: 'Featured City Weather Forecast',
            imageId: '33.jpg',
            previewImage:
                'https://camo.githubusercontent.com/9253d5b1312c7723cb62b2f0e889cb567cbc1175eb82a5d30895e8cb2b30f123/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d786c397a45753772335644584f5f4f5a474d3161372e706e673f763d31',
            prompt: 'From a clear 45° overhead angle, show an isometric miniature model scene featuring [Shanghai Oriental Pearl Tower, The Bund] and other characteristic city buildings, with weather effects cleverly integrated into the scene, soft cloudy weather gently interacting with the city. Use physically-based realistic rendering (PBR) and realistic lighting effects, solid color background, clear and simple. The composition uses centered framing, highlighting the precise and delicate beauty of the three-dimensional model. Display "[Shanghai Cloudy 20°C]" at the top of the image with a cloudy weather icon.',
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Translucent Glass Texture Transform',
            imageId: '34.jpg',
            previewImage:
                'https://camo.githubusercontent.com/26762f716c908798fdd198713ea346482f703876642dc776e088a11ec621d73c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d5f61446969377072333163376e4243333065734c742e706e673f763d31',
            prompt: 'Transform the attached image into soft 3D translucent glass with frosted matte effects and detailed textures, original colors, centered on a light gray background, gently floating in space, soft shadows, natural lighting',
            author: '@azed_ai',
            mode: 'edit'
        },
        {
            title: 'Code Style Business Card',
            imageId: '35.jpg',
            previewImage:
                'https://camo.githubusercontent.com/df13c0036d9f88c0374664df82ad0eea0198b765521bb54b127b8437edb6519a/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d59356e785a6374343972356a70444a67657a6968742e706e673f763d31',
            prompt: 'Close-up shot: A hand holding a business card designed to look like a JSON file in VS Code. The code on the card is presented in real JSON syntax highlighting format. The window interface includes typical toolbar icons and title bar, with the title showing as Business Card.json, overall style completely consistent with VS Code interface. Background is slightly blurred, highlighting the business card content.\nThe JSON code on the business card is as follows:\n{\n  "name": "Jamez Bondos",\n  "title": "Your Title",\n  "email": "<EMAIL>",\n  "link": "yourwebsite"\n}',
            author: '@umesh_ai',
            mode: 'generate'
        },
        {
            title: 'LEGO City Landscape',
            imageId: '36.jpg',
            previewImage:
                'https://camo.githubusercontent.com/f95d2fab4d47abf6501175038b12d57649e46ff0599cb3ed96ae45bedde81eed/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d4e632d7641686149487148697369637a69525551352e706e673f763d31',
            prompt: 'Create a highly detailed and colorful LEGO version of Shanghai\'s Bund scene. The foreground presents the classic Bund historical building complex, using LEGO bricks to exquisitely restore Western and neoclassical architectural facades, including details like clock towers, domes, and colonnades. LEGO minifigures are strolling along the river, taking photos, and sightseeing, with classic LEGO cars parked along the streets. The background is the spectacular Huangpu River, assembled with blue translucent LEGO bricks, with LEGO ferries and tour boats on the river. Across the river, Pudong Lujiazui has towering skyscrapers, including the Oriental Pearl Tower, Shanghai Center, Jinmao Tower, and Shanghai World Financial Center, these ultra-modern LEGO skyscrapers are colorful and realistically shaped. The sky is bright LEGO blue, dotted with a few white LEGO brick clouds, presenting a vibrant and modern visual effect overall.',
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Mini 3D Architecture',
            imageId: '37.jpg',
            previewImage:
                'https://camo.githubusercontent.com/45805585582b815a1605fa805d0243240c1f38067c7d6418c029f9e645fcd413/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d30625170484970696d594c4c72534a3871653643362e706e673f763d31',
            prompt: '3D Q-version mini style, a whimsical mini Starbucks café that looks like a giant takeaway coffee cup, complete with lid and straw. The building has two floors, with large glass windows clearly showing the warm and exquisite interior design: wooden furniture, warm lighting, and busy baristas. There are cute little figurines walking or sitting on the street, surrounded by benches, street lamps, and plant pots, creating a charming corner of the city. The overall style adopts urban miniature landscape style, rich in detail and realistic, with soft lighting presenting a pleasant afternoon feeling.',
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Creative Plant Pot',
            imageId: '38.jpg',
            previewImage:
                'https://camo.githubusercontent.com/21c44f84989b1176b77bf9bda731611c7b9f7081db4be73c0d67d10bcc9a4d5f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4b4d76665a6b4c4675764d457a437a4254387977722e706e673f763d31',
            prompt: 'A high-quality photo showing a cute ceramic [object/animal] shaped flower pot with a smooth surface, filled with various vibrant succulents and green plants, including spiky haworthia, rosette-shaped echeveria, and delicate white small flowers. The pot has a friendly face, placed on a soft neutral background, using diffused natural lighting, showing delicate textures and color contrasts, with simple composition and extremely minimalist style.',
            author: '@azed_ai',
            mode: 'generate'
        },
        {
            title: '"Extremely Ordinary" iPhone Selfie',
            imageId: '39.jpg',
            previewImage:
                'https://camo.githubusercontent.com/a6b07bb2170ebd24d3de178a711d25442a1e5373b86895e934961f20519c2950/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d524566655f50666759667a363766356c6961664b752e706e673f763d31',
            prompt: 'Please draw an extremely ordinary iPhone selfie, without clear subject or compositional sense, just like a casual snapshot. The photo has slight motion blur, uneven sunlight or indoor lighting causing slight overexposure. Awkward angle, chaotic composition, overall presenting a deliberately mediocre feeling - like a selfie accidentally taken when pulling the phone out of pocket. The protagonists are Eason Chan and Nicholas Tse, at night, next to Hong Kong Convention and Exhibition Centre, by Victoria Harbour in Hong Kong.',
            author: '@jiamimaodashu',
            mode: 'generate'
        },
        {
            title: 'Glass Material Retexture',
            imageId: '40.jpg',
            previewImage:
                'https://camo.githubusercontent.com/f6ea76545847586388ceb6dc749054b2a91be35fe42c51eb9f2e3cdd31337ebc/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d51436453414e324979696779485656706c7058474c2e706e673f763d31',
            prompt: 'retexture the image attached based on the json below:\n\n{\n  "style": "photorealistic",\n  "material": "glass",\n  "background": "plain white",\n  "object_position": "centered",\n  "lighting": "soft, diffused studio lighting",\n  "camera_angle": "eye-level, straight-on",\n  "resolution": "high",\n  "aspect_ratio": "2:3",\n  "details": {\n    "reflections": true,\n    "shadows": false,\n    "transparency": true\n  }\n}',
            author: '@egeberkina',
            mode: 'edit'
        },
        {
            title: 'Crystal Ball Story Scene',
            imageId: '41.jpg',
            previewImage:
                'https://camo.githubusercontent.com/1a623fc0c48774dd44d9ac8749b5ecc2eb91f3b1911eb47f2bc58e08f0442491/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d7463504a594a71576853694c42742d4b4d6e7579442e706e673f763d31',
            prompt: 'An exquisite crystal ball quietly placed on a warm and soft desktop by the window, with a blurred and hazy background in warm tones. Warm sunlight gently penetrates the crystal ball, refracting golden light spots, warmly illuminating the surrounding dim space. Inside the crystal ball naturally presents a miniature three-dimensional world themed on {Chang\'e Flying to the Moon}, delicate and beautiful dreamy 3D landscape, with characters and objects all in cute Q-version styling, exquisite and beautiful, full of lively emotional interactions between each other. The overall atmosphere is full of East Asian fantasy colors, extremely rich in detail, presenting a magical realism-like wonderful texture. The entire scene is poetic and dreamlike, magnificent and elegant, radiating warm and soft light, as if given life in the warm light and shadow.',
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Nostalgic Anime Movie Poster',
            imageId: '42.png',
            previewImage:
                'https://github.com/JimmyLv/awesome-nano-banana/raw/main/cases/76/example_anime_nostalgic_poster.png',
            prompt: '{The Lord of the Rings} style anime movie poster, with anime art style of "High School DXD". The poster shows obvious crease marks from long-term repeated folding, causing physical damage and scratches in wrinkled areas, with colors fading in some places. The surface is covered with irregular creases, fold marks, and scratches, all gradually accumulated during constant moving, like an irreversible entropy increase process continuously expanding.\\nHowever, the beautiful memories preserved in our hearts remain intact. When you gaze at this poster full of nostalgic atmosphere, what you feel is the emotional essence carried by those collectibles that have become incredibly precious over time.',
            author: 'photis (Sora)',
            mode: 'generate'
        },
        {
            title: 'Social Media Frame Fusion',
            imageId: '43.jpg',
            previewImage:
                'https://camo.githubusercontent.com/fd39233256cad07cd22688a18d7d24d73c81ae825e4571c7d48faaa374bfe4a9/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d386b66617335364a63332d6631354f3839707a4b4e2e706e673f763d31',
            prompt: 'Create a stylized 3D Q-version character based on the attached photo, accurately preserving the person\'s facial features and clothing details. The character\'s left hand makes a heart gesture (with red heart elements above the fingers), playfully sitting on the edge of a giant Instagram frame with legs hanging outside the frame. The frame top displays username "Beauty", with social media icons (likes, comments, shares) floating around.',
            author: '@dotey',
            mode: 'edit'
        },
        {
            title: 'Fictional Tweet Screenshot',
            imageId: '44.jpg',
            previewImage:
                'https://camo.githubusercontent.com/5b13e965a80e2d8ae3d5889ac3495276a76638dc1edb7d05f60c97841c78ef7f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d354e415041634d4e534c6c376a38507558356d6e742e706e673f763d31',
            prompt: 'Einstein\'s ultra-realistic style tweet posted just after completing the theory of relativity. Includes a selfie photo with clearly visible chalkboard and scribbled formulas in the background. The tweet shows Nikola Tesla liked the content below.',
            author: '@egeberkina',
            mode: 'generate'
        },
        {
            title: 'Emoji Tufted Carpet',
            imageId: '45.jpg',
            previewImage:
                'https://camo.githubusercontent.com/583b26fdf81dac240b845755312040fd0dae8e85e878bcf32b014ab59e125b4c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d673165446973306430696149454b4c6e6b555361612e706e673f763d31',
            prompt: 'Create an image showing a colorful, hand-tufted carpet shaped like the 🦖 emoji, laid on a minimalist floor background. The carpet design is bold and playful, with soft fluffy texture and thick line details. Shot from above using natural lighting, overall style with a quirky DIY aesthetic. Bright colors, cartoon-like outline, tactile and cozy material—similar to handmade tufted art carpets.',
            author: '@gizakdag',
            mode: 'generate'
        },
        {
            title: 'Colorful Vector Art Poster',
            imageId: '46.jpg',
            previewImage:
                'https://camo.githubusercontent.com/4ffb81b3047df5f83649540ebaeb6cff34583c4b65c43819249b37610500bd77/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d347a38794e2d45564852427468546e704342384b612e706e673f763d31',
            prompt: 'Location is "London, United Kingdom", generate a summer colorful vector art poster with large "LONDON" title at the top and smaller "UNITED KINGDOM" title below',
            author: '@michaelrabone',
            mode: 'generate'
        },
        {
            title: 'Cloud Art',
            imageId: '47.jpg',
            previewImage:
                'https://camo.githubusercontent.com/48ab315960b037955aaa2349972ef99a880c791d9bcc1ada88692691e7b85538/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d75664b5152552d307a58586c4143745f415f444e642e706e673f763d31',
            prompt: 'Generate a photo: capturing a daytime scene where scattered clouds in the sky form the shape of [subject/object], located above [location].',
            author: '@umesh_ai',
            mode: 'generate'
        },
        {
            title: '8-bit Pixel Icon',
            imageId: '48.jpg',
            previewImage:
                'https://camo.githubusercontent.com/83e38ad7752cde79777020605aac1e000faec10e8b77ee4dc27a177f30032d6f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4830453845715131306a72626530643871373133372e706e673f763d31',
            prompt: 'Create a minimalist 8-bit pixel style [🍔] logo, centered on a pure white background. Use a limited retro color palette with pixelated details, sharp edges, and clean block forms. The logo should be simple, iconic, and clearly recognizable in pixel art style—inspired by classic arcade game aesthetics.',
            author: '@egeberkina',
            mode: 'generate'
        },
        {
            title: 'Emoji Inflatable Cushion',
            imageId: '49.jpg',
            previewImage:
                'https://camo.githubusercontent.com/e5342b40860c8b46ad941a3c14813ea47cddc62c025a8776bc676ef09922574b/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d30514e774a386f725854714c474e4e746f4d3255712e706e673f763d31',
            prompt: 'Create a high-resolution 3D render designing [🥹] as an inflated, bulging object. The shape should be soft, rounded, and air-filled—similar to a plush balloon or inflatable toy. Use smooth matte material with subtle fabric creases and stitching to enhance the inflated effect. The overall form should be slightly irregular and softly sagging, with soft shadows and gentle lighting to highlight volume and realism. Place it on a clean, minimalist background (light gray or light blue), overall style should remain playful yet sculptural.',
            author: '@gizakdag',
            mode: 'generate'
        },
        {
            title: 'Paper Craft Style Emoji Icon',
            imageId: '50.jpg',
            previewImage:
                'https://camo.githubusercontent.com/b7a7c89709c5637e3dc6c5f53aa869c009d14009a01057e4862bf055495f18fa/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d536f5a50325a773148435258474f4e46645a5344732e706e673f763d31',
            prompt: 'A paper craft style "🔥" icon floating on a pure white background. This emoji is handmade from colored paper cutouts with visible paper texture, creases, and layered shapes. It casts soft shadows below, creating a sense of lightness and dimensionality. The overall design is simple, interesting, and clean, with the image centered and surrounded by ample white space. Use soft studio lighting to highlight paper texture and edges.',
            author: '@egeberkina',
            mode: 'generate'
        },
        {
            title: 'Passport Entry Stamp',
            imageId: '51.jpg',
            previewImage:
                'https://camo.githubusercontent.com/9c7ce999c65901c3b9777d79b8df5019e03aaa2ea9a6b7e2c9d2cec432d9fbab/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d75766245316f334b396c38365a57314e31704e4d4c2e706e673f763d31',
            prompt: 'Create a realistic passport page with an entry stamp for [Beijing, China]. The stamp should read "Welcome to Beijing" in bold English, designed as circular or oval with decorative borders. The stamp should include "ARRIVAL" text and a fictional date like "April 16, 2025". Add subtle outlines of {Forbidden City} as background details in the stamp. Use dark blue or red ink with slight bleeding to enhance realism. The stamp should be slightly tilted as if hand-pressed. The passport page should show clear paper texture and security patterns.',
            author: '@M_w14_',
            mode: 'generate'
        },
        {
            title: 'Physical Destruction Effect Card',
            imageId: '52.jpg',
            previewImage:
                'https://camo.githubusercontent.com/9d16d254ef57b11758a6d02d6afbbdf7aaa4a29bb15c934637494d5961df4d0f/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d424c69697034377353314d5a327036526f55534a4e2e706e673f763d31',
            prompt: 'An ultra-realistic, cinematic illustration depicting Lara Croft dynamically bursting through the border of an "Archaeological Adventure" trading card. She is mid-jump or rope-swinging, wearing iconic adventure gear, possibly using dual pistols with muzzle flashes helping to shatter the card\'s ancient stone-carved border, creating visible dimensional rupture effects around the breach, such as energy cracks and spatial distortions, causing dust and debris to scatter. Her body bursts forward with obvious motion depth, breaking through the card\'s plane, with the card interior (background) depicting dense jungle ruins or trap-filled ancient tomb interiors. Card debris mixes with crumbling stones, flying vines, ancient coin fragments, and spent shell casings. The "Archaeological Adventure" title and "Lara Croft" name (with a stylized artifact icon) are visible on the remaining, cracked and weathered portions of the card. Adventure-filled, dynamic lighting highlights her athletic ability and dangerous environment.',
            author: '@op7418',
            mode: 'generate'
        },
        {
            title: 'Fashion Magazine Cover Style',
            imageId: '53.jpg',
            previewImage:
                'https://camo.githubusercontent.com/f06bcee6af14975b53382123ac726fe714fa531b3378e9838a316a62cee318e7/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4b2d7a4d526c7a753379396245724a68356f4444652e706e673f763d31',
            prompt: 'A beautiful woman wearing a pink qipao, adorned with exquisite floral decorations, her hair dotted with colorful flowers, neck decorated with elegant white lace collar. One of her hands gently holds several large butterflies. The overall shooting style presents high-definition detail texture, similar to fashion magazine cover design, with text "FASHION DESIGN" marked in the center top of the photo. The background uses simple pure light gray to highlight the main subject.',
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Voxel Style 3D Icon Conversion',
            imageId: '54.jpg',
            previewImage:
                'https://camo.githubusercontent.com/812429b9b49df01b2df8d17fcc3e5a044eb441489da71bddf1bf499c434a4c94/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d72657472792d44655052456a4474336e7a366e78745153574374622e706e673f763d31',
            prompt: 'Convert image/description/emoji to voxel 3D icon like the reference image, Octane render, 8k',
            author: '@BrettFromDJ',
            mode: 'edit'
        },
        {
            title: 'RPG Style Character Card Creation',
            imageId: '55.jpg',
            previewImage:
                'https://camo.githubusercontent.com/8ff51afd35ba86f0e12ce304f0c651059f5b8ac1bb549150d1fa15bcef58fbcb/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f34342f6578616d706c655f7270675f636172645f64657369676e65722e706e67',
            prompt: 'Create an RPG collectible-style digital character card. Character set as {Programmer}, standing confidently with tools or symbols related to their profession. Presented in 3D cartoon style with soft lighting, showing distinct personality. Add skill bars or attribute values, such as [Skill1 +x], [Skill2 +x], like Creativity +10, UI/UX +8. Add title banner at the top of the card and character nameplate at the bottom. Card border should be clean and sharp, like real collectible figure packaging boxes. Background should match the professional theme. Use warm highlights and colors that match professional characteristics for coloring.',
            author: '@berryxia_ai',
            mode: 'generate'
        },
        {
            title: 'Q-Version Cute Russian Nesting Dolls',
            imageId: '56.jpg',
            previewImage:
                'https://camo.githubusercontent.com/0a83915cad66e1b1f35aed3e0e0a65addaa92c82be9373b4df115ed458114b11/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f34332f6578616d706c655f6d617472796f73686b615f706561726c5f65617272696e672e706e67',
            prompt: 'Transform the person in the image into Q-version cute Russian nesting dolls 🪆, five in total from large to small, placed on an exquisite wooden table, banner 3:2 ratio',
            author: '@ZHO_ZHO_ZHO',
            mode: 'edit'
        },
        {
            title: '3D Family Wedding Photo',
            imageId: '57.jpg',
            previewImage:
                'https://camo.githubusercontent.com/1f0fae059d027f42d34cf2832eb804d73431e1e98ec118a01395e4ba6f8817a8/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d5a6848674b6b727951655f653632674a794d706a382e706e673f763d31',
            prompt: 'Convert the people in the photo into Q-version 3D characters, parents in wedding attire, child as a beautiful flower girl. Parents in Western wedding attire, father in formal suit, mother in wedding dress. Child holding flowers. Background is a colorful flower arch. Except for the characters being 3D Q-version, everything else in the environment is realistic. The whole scene is placed in a photo frame.',
            author: '@balconychy',
            mode: 'edit'
        },
        {
            title: '3D Q-Version Couple Snow Globe',
            imageId: '58.jpg',
            previewImage:
                'https://camo.githubusercontent.com/e99ef2df7acf4c797fadaba52718b901e2634460ac545482fe91b27f9fa62fec/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f34322f6578616d706c655f33645f715f736e6f77676c6f62655f636f75706c652e706e67',
            prompt: 'Convert the characters in the attached image into a snow globe scene. Overall environment: snow globe placed on a desktop by the window, blurred background, warm tones. Sunlight passes through the sphere, casting golden light spots, illuminating the surrounding darkness. Inside the snow globe: characters are cute Q-version 3D styling, full of love in each other\'s eyes.',
            author: '@balconychy',
            mode: 'edit'
        },
        {
            title: 'Miniature Scene (Monkey King vs White Bone Demon)',
            imageId: '59.png',
            previewImage:
                'https://github.com/JimmyLv/awesome-nano-banana/raw/main/cases/41/example_miniature_journey_west.png',
            prompt: 'Miniature scene presentation using tilt-shift photography techniques, presenting Q-version [Monkey King vs White Bone Demon] scene',
            author: '@dotey',
            mode: 'generate'
        },
        {
            title: 'Real Object and Hand-drawn Doodle Creative Ad',
            imageId: '60.jpg',
            previewImage:
                'https://camo.githubusercontent.com/2b6307f6e906fced7e675614c25fbed6a5e49d47544a050e8e6793a7c2bf0543/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d4274303535695734374f557152444f682d4b30675a2e706e673f763d31',
            prompt: `A minimalist and creative advertisement set on a pure white background.
A real [real object] combined with hand-drawn black ink doodles, with loose and playful lines. The doodles depict: [doodle concept and interaction: interacting with the object in clever, imaginative ways]. Add bold black [advertising copy] text at the top or middle. Place [brand logo] clearly at the bottom. The visual effect should be simple, interesting, high contrast, and cleverly conceived.`,
            author: '@azed_ai',
            mode: 'generate'
        },
        {
            title: 'Cute Warm Knitted Doll',
            imageId: '61.jpg',
            previewImage:
                'https://camo.githubusercontent.com/3cc59f2a7296dd0f2e4a8d81e8bfb06dad2c9fb00f92a90589dae15c758d919c/68747470733a2f2f626962696770742d617070732e636861747669642e61692f63686174696d672f67656d696e692d467056545546574b3264686431475379734f4664682e706e673f763d31',
            prompt: 'A close-up, professionally composed photo showing a hand-crocheted yarn doll being gently held by both hands. The doll has a rounded shape, [uploaded image] character\'s cute Q-version image, with bright color contrasts and rich details. The hands holding the doll are natural and gentle, with clear finger postures, natural skin texture and light-shadow transitions, showing warm and realistic touch. Background is slightly blurred, showing indoor environment with warm wooden desktop and natural light from windows, creating a comfortable and intimate atmosphere. The overall image conveys exquisite craftsmanship and cherished warm emotions.',
            author: '@ZHO_ZHO_ZHO',
            mode: 'edit'
        },
        {
            title: 'Japanese Two-Panel Manga',
            imageId: '62.jpg',
            previewImage:
                'https://camo.githubusercontent.com/f183d55c3ec78ff3aa0b8ad162592e76660fd6eded824c09ac32ec090e5d3222/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f34302f6578616d706c655f74776f5f70616e656c5f6d616e67615f707265736964656e742e706e67',
            prompt: 'Create a Japanese moe-style two-panel manga, arranged vertically, theme: Girl President\'s Work Daily Life. Character image: Convert the uploaded attachment to Japanese moe-style cartoon girl image style, retaining all details of the original image, such as clothing (suit), hairstyle (bright golden yellow), facial features, etc. First panel: - Expression: pitiful and dejected, single hand supporting cheek - Text box: "What to do! He won\'t talk to me! (；´д｀)" - Scene: warm-toned office, American flag behind, pile of hamburgers on desk, vintage red rotary phone, character on left side of frame, phone on right. Second panel: - Expression: gritting teeth, furious, face flushed red - Action: slamming desk hard, hamburgers bouncing up - Speech bubble: "Hmph! Double the tariffs! Not talking to me is their loss! ( `д´ )" - Scene: same as first panel but in chaos. Other notes: - Text uses simple cute handwriting, overall style cute and interesting. - Composition is full and lively, please reserve enough space for text display, appropriate white space. - Image ratio 2:3. - Overall bright colors, highlighting cartoon style.',
            author: '@hellokaton',
            mode: 'edit'
        },
        {
            title: 'Fantasy Cartoon Illustration',
            imageId: '63.jpg',
            previewImage:
                'https://camo.githubusercontent.com/d50b0d3392e3a7feb4acd0453f8543edaa8ce5823c8287b22980df1bd55b7305/68747470733a2f2f63646e2e6a7364656c6976722e6e65742f67682f6a616d657a2d626f6e646f732f617765736f6d652d677074346f2d696d616765732f63617365732f33392f6578616d706c655f66616e746173795f636f6d70757465725f686561645f706f7274616c2e706e67',
            prompt: 'A cartoon-style character with a head that is a computer monitor with a smiley face, wearing gloves and boots, happily jumping through a glowing blue circular portal, with a lush fantasy forest landscape in the background. The forest is rich in detail with tall trees, mushrooms, flowers, peaceful rivers, floating islands, and an atmospheric starry night sky with multiple moons. Overall using bright vivid colors with soft light effects, fantasy illustration style.',
            author: '@dotey',
            mode: 'generate'
        }
    ];
