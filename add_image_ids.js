const fs = require('fs');

// 讀取 script.txt 文件
let scriptContent = fs.readFileSync('script.txt', 'utf8');

// 計數器
let imageCounter = 1;

// 替換每個 previewImage 行，添加 imageId
scriptContent = scriptContent.replace(
    /({\s*\n\s*title:\s*'[^']*',\s*\n)(\s*previewImage:\s*'[^']*',)/g,
    (match, titlePart, previewImagePart) => {
        // 確定文件擴展名
        const urlMatch = previewImagePart.match(/'([^']*)'/)
        let extension = '.jpg'; // 默認擴展名
        
        if (urlMatch && urlMatch[1]) {
            const url = urlMatch[1];
            if (url.includes('.png')) {
                extension = '.png';
            }
        }
        
        const imageId = `${imageCounter}${extension}`;
        imageCounter++;
        
        return titlePart + `            imageId: '${imageId}',\n` + previewImagePart;
    }
);

// 寫入修改後的內容
fs.writeFileSync('script_updated.txt', scriptContent);

console.log(`已處理 ${imageCounter - 1} 個對象，添加了 imageId`);
console.log('修改後的文件已保存為 script_updated.txt');
